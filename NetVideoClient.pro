#-------------------------------------------------
#
# Project created by QtC<PERSON> 2017-11-08T09:45:51
#
#-------------------------------------------------

QT       += core gui network sql

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

TARGET = CameraVideo
TEMPLATE = app

RC_ICONS = images/app.ico

# FFmpeg库路径配置
message("Building with FFmpeg support")

FFMPEG_INCLUDE_PATH = $$PWD/ffmpeg/include
FFMPEG_LIB_PATH = $$PWD/ffmpeg/lib
FFMPEG_BIN_PATH = $$PWD/ffmpeg/bin

# FFmpeg头文件路径
INCLUDEPATH += $$FFMPEG_INCLUDE_PATH

# FFmpeg库文件 - 添加在链接时需要的FFmpeg库，适配FFmpeg 5.1版本
win32-g++ {
    # MinGW环境下，根据实际检查到的库文件名称
    message("Building with MinGW - using FFmpeg libraries")
    
    # 方法1：使用MinGW下的动态链接库
    LIBS += -L$$FFMPEG_LIB_PATH \
            -lavformat \
            -lavcodec \
            -lavutil \
            -lswscale \
            -lswresample \
            -lws2_32 # Windows Socket库，FFmpeg可能需要
    
    # 方法2：使用静态库
    # LIBS += $$FFMPEG_LIB_PATH/avformat.lib \
    #         $$FFMPEG_LIB_PATH/avcodec.lib \
    #         $$FFMPEG_LIB_PATH/avutil.lib \
    #         $$FFMPEG_LIB_PATH/swscale.lib \
    #         $$FFMPEG_LIB_PATH/swresample.lib \
    #         -lws2_32
} else {
    # MSVC或其他环境
    message("Building with non-MinGW compiler")
    LIBS += -L$$FFMPEG_LIB_PATH \
            -lavformat \
            -lavcodec \
            -lavutil \
            -lswscale \
            -lswresample
}
        
# 定义宏，避免FFmpeg弃用警告
DEFINES += __STDC_CONSTANT_MACROS

# 复制FFmpeg DLL文件到目标目录
win32 {
    CONFIG(debug, debug|release) {
        ffmpeg_dlls.files = $$FFMPEG_BIN_PATH/avformat-59.dll \
                           $$FFMPEG_BIN_PATH/avcodec-59.dll \
                           $$FFMPEG_BIN_PATH/avutil-57.dll \
                           $$FFMPEG_BIN_PATH/swscale-6.dll \
                           $$FFMPEG_BIN_PATH/swresample-4.dll
        ffmpeg_dlls.path = $$OUT_PWD/debug
        INSTALLS += ffmpeg_dlls
        
        # 手动复制DLL
        QMAKE_POST_LINK += $$quote(copy /y \"$$FFMPEG_BIN_PATH\\avformat-59.dll\" \"$$OUT_PWD\\debug\\\"$$escape_expand(\n\t))
        QMAKE_POST_LINK += $$quote(copy /y \"$$FFMPEG_BIN_PATH\\avcodec-59.dll\" \"$$OUT_PWD\\debug\\\"$$escape_expand(\n\t))
        QMAKE_POST_LINK += $$quote(copy /y \"$$FFMPEG_BIN_PATH\\avutil-57.dll\" \"$$OUT_PWD\\debug\\\"$$escape_expand(\n\t))
        QMAKE_POST_LINK += $$quote(copy /y \"$$FFMPEG_BIN_PATH\\swscale-6.dll\" \"$$OUT_PWD\\debug\\\"$$escape_expand(\n\t))
        QMAKE_POST_LINK += $$quote(copy /y \"$$FFMPEG_BIN_PATH\\swresample-4.dll\" \"$$OUT_PWD\\debug\\\"$$escape_expand(\n\t))
    } else {
        ffmpeg_dlls.files = $$FFMPEG_BIN_PATH/avformat-59.dll \
                           $$FFMPEG_BIN_PATH/avcodec-59.dll \
                           $$FFMPEG_BIN_PATH/avutil-57.dll \
                           $$FFMPEG_BIN_PATH/swscale-6.dll \
                           $$FFMPEG_BIN_PATH/swresample-4.dll
        ffmpeg_dlls.path = $$OUT_PWD/release
        INSTALLS += ffmpeg_dlls
        
        # 手动复制DLL
        QMAKE_POST_LINK += $$quote(copy /y \"$$FFMPEG_BIN_PATH\\avformat-59.dll\" \"$$OUT_PWD\\release\\\"$$escape_expand(\n\t))
        QMAKE_POST_LINK += $$quote(copy /y \"$$FFMPEG_BIN_PATH\\avcodec-59.dll\" \"$$OUT_PWD\\release\\\"$$escape_expand(\n\t))
        QMAKE_POST_LINK += $$quote(copy /y \"$$FFMPEG_BIN_PATH\\avutil-57.dll\" \"$$OUT_PWD\\release\\\"$$escape_expand(\n\t))
        QMAKE_POST_LINK += $$quote(copy /y \"$$FFMPEG_BIN_PATH\\swscale-6.dll\" \"$$OUT_PWD\\release\\\"$$escape_expand(\n\t))
        QMAKE_POST_LINK += $$quote(copy /y \"$$FFMPEG_BIN_PATH\\swresample-4.dll\" \"$$OUT_PWD\\release\\\"$$escape_expand(\n\t))
    }
}

# 链接库文件 - 根据编译模式使用不同路径
CONFIG(debug, debug|release) {
    LIBS += -L"./debug" -lsqlite3 -lzf_KeyCheck
} else {
    LIBS += -L"./release" -lsqlite3 -lzf_KeyCheck
}

SOURCES += main.cpp\
        mainwindow.cpp \
    onevideo.cpp \
    mythread.cpp \
    configdialog.cpp \
    fullscreenvideo.cpp \
    camerascanner.cpp \
    camerascanwidget.cpp \
    videosurface.cpp \
    configmanager.cpp \
    iconbutton.cpp \
    singleapplication.cpp \
    logmanager.cpp

HEADERS  += mainwindow.h \
    onevideo.h \
    mythread.h \
    configdialog.h \
    fullscreenvideo.h \
    camerascanner.h \
    camerascanwidget.h \
    videosurface.h \
    configmanager.h \
    iconbutton.h \
    singleapplication.h \
    zf_UdiskAndSoftKeyCheck.h \
    globalsignal.h \
    logmanager.h

FORMS    += \
    mainwindow.ui \
    camerascanwidget.ui \
    onevideo.ui \
    configdialog.ui

RESOURCES += \
    res.qrc
