#include "mythread.h"
#include <QTcpSocket>
#include <QByteArray>
#include <QImage>
#include <QNetworkRequest>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QEventLoop>
#include <QTimer>
#include <QUrl>
#include <QDebug>
#include <QRegExp>
#include <QProcess>
#include <QTemporaryFile>
#include <QDir>
#include <QCoreApplication>
#include <QTime>
#include <QElapsedTimer>
#include <QMutexLocker>
#include "logmanager.h"
// FFmpeg头文件
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
#include <libavutil/opt.h>

// 定义LOG_DEBUG_ONCE宏，只打印一次日志
#define LOG_DEBUG_ONCE(msg)         \
	do                              \
	{                               \
		static bool logged = false; \
		if (!logged)                \
		{                           \
			LOG_DEBUG(msg);         \
			logged = true;          \
		}                           \
	} while (0)

MyThread::MyThread(QObject *parent) : QThread(parent),
									  formatContext(nullptr), codecContext(nullptr), frame(nullptr), packet(nullptr), swsContext(nullptr), videoStreamIndex(-1),
									  audioOutput(nullptr), audioDevice(nullptr), audioBufferDevice(nullptr), swrContext(nullptr), audioInitialized(false)
{
	isRunning = false;
	isMuted = true;			 // 默认静音
	m_threadStopped = false; // 初始化线程停止标志
	recordProcess = nullptr;
}

MyThread::MyThread(QString ip, int port, QObject *parent) : QThread(parent),
															formatContext(nullptr), codecContext(nullptr), frame(nullptr), packet(nullptr), swsContext(nullptr), videoStreamIndex(-1),
															audioOutput(nullptr), audioDevice(nullptr), audioBufferDevice(nullptr), swrContext(nullptr), audioInitialized(false)
{
	this->ip = ip;
	this->port = port;
	isRunning = false;
	isMuted = true;			 // 默认静音
	m_threadStopped = false; // 初始化线程停止标志
	recordProcess = nullptr;
}

MyThread::MyThread(QString rtspUrl, QObject *parent) : QThread(parent),
													   formatContext(nullptr), codecContext(nullptr), frame(nullptr), packet(nullptr), swsContext(nullptr), videoStreamIndex(-1),
													   audioOutput(nullptr), audioDevice(nullptr), audioBufferDevice(nullptr), swrContext(nullptr), audioInitialized(false)
{
	this->rtspUrl = rtspUrl;
	isRunning = false;
	isMuted = true;			 // 默认静音
	m_threadStopped = false; // 初始化线程停止标志
	recordProcess = nullptr;
}

MyThread::~MyThread()
{
	// 停止所有活动
	isRunning = false;
	m_threadStopped = false;

	// 尝试正常停止线程
	if (QThread::isRunning())
	{
		// 给线程100毫秒的时间正常结束
		if (!wait(100))
		{
			// 如果100毫秒后线程还没结束，强制终止
			LOG_WARNING("Force terminating thread in destructor");
			terminate();
			wait(500); // 等待线程真正结束
		}
	}

	// 清理资源
	mutex.lock();
	cleanupMediaResources();
	cleanupFFmpeg();
	mutex.unlock();

	LOG_DEBUG("MyThread destructor completed");
}

// 新增: 安全停止线程方法
bool MyThread::safeStop(int timeoutMs)
{
	if (!isRunning)
	{
		return true; // 线程已经停止
	}

	// 设置停止标志
	isRunning = false;
	m_threadStopped = false;

	// 等待线程完成
	if (QThread::isRunning())
	{
		// 等待指定时间
		return wait(timeoutMs);
	}

	return true;
}

void MyThread::setRunning(bool running)
{
	if (!running && isRunning)
	{
		// 如果要停止线程，使用安全停止方法
		safeStop();
	}
	else
	{
		isRunning = running;
	}
}

void MyThread::run()
{
	LOG_DEBUG("MyThread start running in thread:" << QThread::currentThread());

	// 在线程开始时设置运行标志
	isRunning = true;
	m_threadStopped = false;

	// 使用FFmpeg处理RTSP流
	handleRtspWithFFmpeg();

	// 线程即将结束，标记线程已停止
	m_threadStopped = true;

	LOG_DEBUG("MyThread end running in thread:" << QThread::currentThread());
}

// 简化 cleanupMediaResources 方法
void MyThread::cleanupMediaResources()
{
	try
	{
		LOG_DEBUG("开始清理媒体资源");
		LOG_DEBUG("媒体资源清理完成");
	}
	catch (...)
	{
		LOG_ERROR("清理媒体资源时发生未知异常");
	}
}

void MyThread::handleTcpConnection()
{
	QTcpSocket socket;
	connect(&socket, SIGNAL(disconnected()), this, SIGNAL(disconnectSlot()));
	socket.connectToHost(ip, port);
	//	socket.connectToHost("**************", 10086);
	if (!socket.waitForConnected(3000))
	{
		LOG_ERROR("connect failed:" << socket.errorString());
		emit disconnectSlot();
		return;
	}
	else
	{
		LOG_DEBUG("connect success!");
	}

	// 发送HTTP请求
	socket.write("\r\n\r\n");
	if (socket.waitForBytesWritten(3000))
	{
		LOG_DEBUG("send Http Request success!");
	}
	e 简化 cleanupMediaResourcesl
	{
		LOG_DEBUG("send Http Request failed!");
		return;
	}
	// 读取响应
	QByteArray dataStream;
	Q
}
}
}
}
// 从IP构建HTTP图像URLQString MyThread::buildHttpImageUrl(const QString &ip, const QString &username, const QString &password)
{
	// 使用QUrl类来构建URL，以确保正确处理特殊字符
	QUrl url;
	url.setScheme("http");
	url.setHost(ip);
	url.setPath("/image.jpg");

	// 添加认证信息（如果有）
	if (!username.isEmpty() && !password.isEmpty())
	{
		url.setUserName(username);
		url.setPassword(password);
	}

	// 返回URL字符串
	return url.toString();
}

// 测试HTTP图像是否有效
bool MyThread::testHttpImage(const QString &ip, const QString &username, const QString &password, int timeoutMs)
{
	// LOG_DEBUG("111111111111111Testing HTTP image: " << ip << " username: " << username << " password: " << password);
	//  构建HTTP图像URL
	QString url = buildHttpImageUrl(ip, username, password);

	LOG_DEBUG("Testing HTTP image: " << url);

	// 使用严格超时控制
	QElapsedTimer timeoutTimer;
	timeoutTimer.start();

	// 检查URL是否有效
	QUrl qurl(url);
	if (!qurl.isValid())
	{
		LOG_DEBUG("HTTP image request error: Invalid URL format");
		return false;
	}

	// 创建网络请求管理器
	QScopedPointer<QNetworkAccessManager> manager(new QNetworkAccessManager());
	QNetworkRequest request;
	request.setUrl(qurl);

	// 添加基本认证头（另一种认证方式）
	if (!username.isEmpty() && !password.isEmpty())
	{
		// 直接使用原始用户名和密码，不进行URL编码
		QString credentials = username + ":" + password;
		QByteArray auth = "Basic " + credentials.toUtf8().toBase64();
		request.setRawHeader("Authorization", auth);
		LOG_DEBUG("Using HTTP basic auth with credentials: " << username << ":******");
	}

	// 设置较短的超时
	timeoutMs = qMin(timeoutMs, 2000); // 最多2秒

	// 发送请求并等待响应
	QEventLoop loop;
	QTimer timer;
	timer.setSingleShot(true);

	bool requestError = false;
	QString errorMessage;
	QByteArray imageData;

	// 连接定时器超时信号
	QObject::connect(&timer, &QTimer::timeout, [&]()
					 {
		LOG_DEBUG("HTTP image request timeout");
		requestError = true;
		errorMessage = "Request timeout";
		loop.quit(); });

	// 发送请求
	QNetworkReply *reply = manager->get(request);

	// 连接请求完成信号
	QObject::connect(reply, &QNetworkReply::finished, [&]()
					 {
		if (reply->error() == QNetworkReply::NoError) {
			// 获取图像数据
			imageData = reply->readAll();
			
			// 检查是否是有效的图像
			QImage image = QImage::fromData(imageData);
			if (image.isNull()) {
				requestError = true;
				errorMessage = "Invalid image data";
			}
		} else {
			requestError = true;
			errorMessage = reply->errorString();
		}
		
		loop.quit(); });

	// 启动超时计时器
	timer.start(timeoutMs);

	// 等待请求完成或超时
	loop.exec();

	// 释放资源
	reply->deleteLater();

	// 断开连接
	QObject::disconnect(reply, nullptr, nullptr, nullptr);

	// 检查是否成功获取图像
	bool success = !requestError && !imageData.isEmpty();

	if (requestError)
	{
		LOG_DEBUG("HTTP image request error: " << errorMessage);
	}
	else if (imageData.isEmpty())
	{
		LOG_DEBUG("HTTP image data is empty");
		success = false;
	}

	LOG_DEBUG("HTTP image test result: " << (success ? "Success" : "Failed") << " Time: " << timeoutTimer.elapsed() << "ms");

	return success;
}

void MyThread::handleSocketError()
{
	// 使用sender()获取触发信号的socket对象
	QTcpSocket *socket = qobject_cast<QTcpSocket *>(sender());
	if (socket)
	{
		LOG_DEBUG("Socket错误: " << socket->error() << " - " << socket->errorString());

		// 设置标志并发送信号
		isRunning = false;
		emit disconnectSlot();
	}
}

// FFmpeg初始化方法
bool MyThread::initFFmpeg()
{
	LOG_DEBUG("初始化FFmpeg，RTSP URL: " << rtspUrl);

	try
	{
		// 初始化FFmpeg网络功能
		avformat_network_init();

		// 分配格式上下文
		formatContext = avformat_alloc_context();
		if (!formatContext)
		{
			LOG_ERROR("无法分配FFmpeg格式上下文");
			return false;
		}

		// 设置选项，如TCP传输、超时等
		AVDictionary *options = nullptr;
		av_dict_set(&options, "rtsp_transport", "tcp", 0); // 使用TCP传输RTSP
		// av_dict_set(&options, "rtsp_transport", "udp", 0);	  // 使用UDP传输RTSP
		av_dict_set(&options, "stimeout", "5000000", 0);	  // 5秒超时，单位是微秒
		av_dict_set(&options, "buffer_size", "1024000", 0);	  // 缓冲区大小
		av_dict_set(&options, "max_delay", "500000", 0);	  // 最大延迟
		av_dict_set(&options, "reconnect", "1", 0);			  // 断线重连
		av_dict_set(&options, "reconnect_streamed", "1", 0);  // 流媒体重连
		av_dict_set(&options, "reconnect_delay_max", "5", 0); // 最大重连延迟

		// 设置中断回调，用于处理超时
		formatContext->interrupt_callback.callback = [](void *ctx) -> int
		{
			// 超时中断处理
			return 0; // 返回1表示中断
		};
		formatContext->interrupt_callback.opaque = this;

		// 打开输入流
		int ret = avformat_open_input(&formatContext, rtspUrl.toUtf8().constData(), nullptr, &options);
		if (ret < 0)
		{
			char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
			av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
			LOG_ERROR("无法打开RTSP流: " << rtspUrl << ", 错误: " << errBuf);
			return false;
		}

		// 查找流信息
		ret = avformat_find_stream_info(formatContext, nullptr);
		if (ret < 0)
		{
			char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
			av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
			LOG_ERROR("无法找到流信息, 错误: " << errBuf);
			return false;
		}

		// 查找视频流
		videoStreamIndex = -1;
		for (unsigned int i = 0; i < formatContext->nb_streams; i++)
		{
			if (formatContext->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO)
			{
				videoStreamIndex = i;
				break;
			}
		}

		if (videoStreamIndex == -1)
		{
			LOG_ERROR("未找到视频流");
			return false;
		}

		// 获取编解码器
		AVCodecParameters *codecParams = formatContext->streams[videoStreamIndex]->codecpar;
		const AVCodec *codec = avcodec_find_decoder(codecParams->codec_id);
		if (!codec)
		{
			LOG_ERROR("未找到解码器");
			return false;
		}

		// 创建编解码器上下文
		codecContext = avcodec_alloc_context3(codec);
		if (!codecContext)
		{
			LOG_ERROR("无法分配编解码器上下文");
			return false;
		}

		// 将编解码器参数复制到上下文
		ret = avcodec_parameters_to_context(codecContext, codecParams);
		if (ret < 0)
		{
			char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
			av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
			LOG_ERROR("无法复制编解码器参数, 错误: " << errBuf);
			return false;
		}

		// 打开解码器
		ret = avcodec_open2(codecContext, codec, nullptr);
		if (ret < 0)
		{
			char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
			av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
			LOG_ERROR("无法打开解码器, 错误: " << errBuf);
			return false;
		}

		// 分配帧和数据包
		frame = av_frame_alloc();
		if (!frame)
		{
			LOG_ERROR("无法分配帧");
			return false;
		}

		packet = av_packet_alloc();
		if (!packet)
		{
			LOG_ERROR("无法分配数据包");
			return false;
		}

		// 初始化成功
		LOG_DEBUG("FFmpeg初始化成功, 视频流索引: " << videoStreamIndex << ", 分辨率: " << codecContext->width << "x" << codecContext->height << ", 编解码器: " << codec->name);

		return true;
	}
	catch (const std::exception &e)
	{
		LOG_ERROR("FFmpeg初始化过程中发生异常: " << e.what());
		return false;
	}
	catch (...)
	{
		LOG_ERROR("FFmpeg初始化过程中发生未知异常");
		return false;
	}
}

// FFmpeg清理方法
void MyThread::cleanupFFmpeg()
{
	try
	{
		LOG_DEBUG("清理FFmpeg资源");

		// 清理音频资源
		cleanupAudioOutput();

		if (swsContext)
		{
			sws_freeContext(swsContext);
			swsContext = nullptr;
		}

		if (frame)
		{
			av_frame_free(&frame);
			frame = nullptr;
		}

		if (packet)
		{
			av_packet_free(&packet);
			packet = nullptr;
		}

		if (codecContext)
		{
			avcodec_free_context(&codecContext);
			codecContext = nullptr;
		}

		if (formatContext)
		{
			avformat_close_input(&formatContext);
			formatContext = nullptr;
		}

		LOG_DEBUG("FFmpeg资源清理完成");
	}
	catch (...)
	{
		LOG_ERROR("清理FFmpeg资源时发生未知异常");
	}
}

// 使用FFmpeg处理RTSP流
void MyThread::handleRtspWithFFmpeg()
{
	LOG_DEBUG("使用FFmpeg处理RTSP流: " << rtspUrl);

	try
	{
		// 初始化FFmpeg
		if (!initFFmpeg())
		{
			LOG_ERROR("FFmpeg初始化失败");

			isRunning = false;
			m_threadStopped = true;

			emit disconnectSlot();
			return;
		}

		// 开始读取和处理帧
		LOG_DEBUG("开始读取视频帧");

		// 获取视频流的帧率
		double framerate = 25.0; // 默认帧率
		if (formatContext->streams[videoStreamIndex]->avg_frame_rate.num &&
			formatContext->streams[videoStreamIndex]->avg_frame_rate.den)
		{
			framerate = (double)formatContext->streams[videoStreamIndex]->avg_frame_rate.num /
						formatContext->streams[videoStreamIndex]->avg_frame_rate.den;
		}
		int frameInterval = 1000 / (int)framerate; // 帧间隔(毫秒)
		LOG_DEBUG("视频帧率: " << framerate << " fps, 帧间隔: " << frameInterval << " ms");

		// 帧缓存控制 - 参考src中的DEFAULT_FRAME_CACHE
		const int MAX_FRAME_CACHE = 10;
		int queuedFrames = 0;

		// 用于计算帧率
		int frameCount = 0;
		QTime fpsTimer;
		fpsTimer.start();

		// 用于周期性检查线程状态
		QElapsedTimer checkTimer;
		checkTimer.start();

		// 用于帧同步
		QElapsedTimer frameTimer;
		frameTimer.start();

		// 用于检测和处理延迟
		QElapsedTimer delayTimer;
		delayTimer.start();
		int64_t lastPts = AV_NOPTS_VALUE;
		int skipFrameCount = 0;
		int processedFrameCount = 0;
		bool frameDelayDetected = false;

		// 预先准备QImage对象，避免频繁创建和销毁
		QImage processedImage;
		bool hasInitializedImage = false;

		while (true)
		{
			try
			{
				// 周期性检查线程是否应该停止（每100毫秒检查一次）
				if (checkTimer.elapsed() > 100)
				{
					if (!isRunning)
					{
						break;
					}
					checkTimer.restart();
				}

				// 每10秒检查一次是否出现延迟问题
				if (delayTimer.elapsed() > 10000)
				{
					LOG_DEBUG("性能统计: 处理帧数=" << processedFrameCount << ", 丢弃帧数=" << skipFrameCount
													<< ", 当前队列=" << queuedFrames);
					processedFrameCount = 0;
					skipFrameCount = 0;
					delayTimer.restart();
				}

				// 读取下一帧
				int ret = av_read_frame(formatContext, packet);
				if (ret < 0)
				{
					// 遇到错误，可能是流结束或网络问题
					if (ret == AVERROR_EOF || ret == AVERROR(EAGAIN))
					{
						// 流结束或需要等待更多数据
						LOG_DEBUG("等待更多数据或流结束");
						QThread::msleep(100);
						continue;
					}
					else
					{
						// 其他错误
						char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
						av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
						LOG_ERROR("读取帧失败: " << errBuf);

						// 重试几次后再退出
						static int errorCount = 0;
						if (++errorCount > 5)
						{
							LOG_ERROR("连续读取帧失败超过5次，退出处理");
							break;
						}

						QThread::msleep(200);
						continue;
					}
				}

				// 处理视频帧
				if (packet->stream_index == videoStreamIndex)
				{
					// 缓存控制 - 如果队列中的帧过多，丢弃非关键帧
					if (queuedFrames >= MAX_FRAME_CACHE)
					{
						// 当缓存满时，丢弃非关键帧
						if (!(packet->flags & AV_PKT_FLAG_KEY))
						{
							skipFrameCount++;
							av_packet_unref(packet);
							continue; // 跳过这一帧的处理
						}
						// 即使是关键帧，也需要平衡处理速度
						else
						{
							// 减少队列中的帧数（模拟强制处理完一帧）
							queuedFrames = MAX_FRAME_CACHE - 1;
						}
					}

					// 时间戳检查，防止延迟累积
					if (packet->pts != AV_NOPTS_VALUE && lastPts != AV_NOPTS_VALUE)
					{
						// 获取时间基准
						AVRational time_base = formatContext->streams[videoStreamIndex]->time_base;

						// 计算相对于上一帧的时间戳差异（毫秒）
						int64_t pts_diff = av_rescale_q(packet->pts - lastPts,
														time_base,
														AVRational{1, 1000});

						// 计算预期的帧间隔时间（毫秒）
						int expected_interval = frameInterval;

						// 如果时间戳差异超过预期间隔的5倍，检测到严重延迟
						if (pts_diff > expected_interval * 5 && !frameDelayDetected)
						{
							LOG_WARNING("检测到视频延迟: 时间戳差异=" << pts_diff << "ms, 预期间隔=" << expected_interval << "ms");
							frameDelayDetected = true;

							// 遇到严重延迟时，清空队列
							queuedFrames = 0;
						}
					}

					// 更新最后一个时间戳
					if (packet->pts != AV_NOPTS_VALUE)
					{
						lastPts = packet->pts;
					}

					// 增加队列中的帧数
					queuedFrames++;

					// 发送数据包到解码器 - 不需要锁
					ret = avcodec_send_packet(codecContext, packet);
					if (ret < 0)
					{
						char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
						av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
						LOG_ERROR("发送视频数据包失败: " << errBuf);
						av_packet_unref(packet);
						queuedFrames--; // 调整队列计数
						continue;
					}

					// 从解码器接收帧
					bool frameProcessed = false;
					while (ret >= 0 && !frameProcessed)
					{
						// 再次检查线程状态
						if (!isRunning)
						{
							break;
						}

						ret = avcodec_receive_frame(codecContext, frame);
						if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF)
						{
							// 需要更多输入数据或已到流尾
							break;
						}
						else if (ret < 0)
						{
							char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
							av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
							LOG_ERROR("接收视频帧失败: " << errBuf);
							break;
						}

						try
						{
							// 创建或更新缩放上下文
							if (!swsContext)
							{
								swsContext = sws_getContext(
									codecContext->width, codecContext->height, codecContext->pix_fmt,
									codecContext->width, codecContext->height, AV_PIX_FMT_RGB24,
									SWS_BILINEAR, nullptr, nullptr, nullptr);

								if (!swsContext)
								{
									LOG_ERROR("无法创建缩放上下文");
									break;
								}
							}

							// 创建QImage接收转换后的数据 - 重用已有图像对象
							if (!hasInitializedImage || processedImage.width() != codecContext->width ||
								processedImage.height() != codecContext->height)
							{
								processedImage = QImage(codecContext->width, codecContext->height, QImage::Format_RGB888);
								hasInitializedImage = true;
							}

							// 设置目标数据指针和行大小
							uint8_t *destData[4] = {processedImage.bits(), nullptr, nullptr, nullptr};
							int destLinesize[4] = {processedImage.bytesPerLine(), 0, 0, 0};

							// 执行颜色空间转换 - 这个操作不需要锁
							sws_scale(swsContext, frame->data, frame->linesize, 0,
									  codecContext->height, destData, destLinesize);

							// 基于帧率控制帧发送节奏
							int elapsed = frameTimer.elapsed();
							if (elapsed < frameInterval && !frameDelayDetected)
							{
								// 等待适当的时间以保持正确的帧率
								QThread::msleep(frameInterval - elapsed);
							}
							frameTimer.restart();

							// 在这里进行传输前的缓冲区处理
							QImage copyToSend = processedImage.copy(); // 创建副本用于发送

							// 更新lastFrame - 使用最小的互斥锁范围
							mutex.lock();
							lastFrame = copyToSend; // 深拷贝，避免数据竞争
							mutex.unlock();

							// 再次检查线程状态
							if (isRunning)
							{
								emit transmitData(copyToSend);
								processedFrameCount++;
								frameProcessed = true;

								// 成功处理一帧后，减少队列计数
								queuedFrames--;
							}

							// 计算并显示帧率
							frameCount++;
							if (fpsTimer.elapsed() >= 5000)
							{ // 每5秒显示一次帧率
								double fps = frameCount / (fpsTimer.elapsed() / 1000.0);
								LOG_DEBUG("当前帧率: " << fps << " fps, 队列中帧数: " << queuedFrames);
								frameCount = 0;
								fpsTimer.restart();
							}
						}
						catch (const std::exception &e)
						{
							LOG_ERROR("处理视频帧数据时发生异常: " << e.what());
						}
						catch (...)
						{
							LOG_ERROR("处理视频帧数据时发生未知异常");
						}
					}
				}

				// 释放数据包
				av_packet_unref(packet);

				// 防止CPU过载，动态调整睡眠时间
				if (frameDelayDetected)
				{
					// 如果检测到延迟，不睡眠或睡眠很短时间
					QThread::yieldCurrentThread(); // 让出时间片给其他线程
				}
				else if (queuedFrames < MAX_FRAME_CACHE / 2)
				{
					// 如果队列不满，适当增加睡眠时间以节省CPU
					QThread::msleep(5);
				}
				else
				{
					// 正常情况下的睡眠时间
					QThread::msleep(1);
				}
			}
			catch (const std::exception &e)
			{
				LOG_ERROR("处理帧时发生异常: " << e.what());
				QThread::msleep(500); // 出错后稍微暂停一下
			}
			catch (...)
			{
				LOG_ERROR("处理帧时发生未知异常");
				QThread::msleep(500);
			}
		}

		// 清理资源 - 使用互斥锁保护
		mutex.lock();
		cleanupFFmpeg();
		mutex.unlock();

		LOG_DEBUG("FFmpeg处理结束");
	}
	catch (const std::exception &e)
	{
		LOG_ERROR("FFmpeg处理过程中发生异常: " << e.what());

		isRunning = false;
		m_threadStopped = true;

		emit disconnectSlot();
	}
	catch (...)
	{
		LOG_ERROR("FFmpeg处理过程中发生未知异常");

		isRunning = false;
		m_threadStopped = true;

		emit disconnectSlot();
	}
}

// 初始化音频输出
bool MyThread::initAudioOutput(int sampleRate, int channels, int sampleFormat)
{
	if (audioInitialized)
	{
		// 已经初始化过，直接返回
		return true;
	}

	LOG_DEBUG("初始化音频输出: 采样率=" << sampleRate << ", 声道数=" << channels << ", 格式=" << sampleFormat);

	try
	{
		// 设置音频输出参数
		QAudioFormat format;
		format.setSampleRate(sampleRate);
		format.setChannelCount(channels);
		format.setSampleSize(16); // 16位PCM
		format.setCodec("audio/pcm");
		format.setByteOrder(QAudioFormat::LittleEndian);
		format.setSampleType(QAudioFormat::SignedInt);

		// 检查格式是否支持
		QAudioDeviceInfo info(QAudioDeviceInfo::defaultOutputDevice());
		if (!info.isFormatSupported(format))
		{
			LOG_WARNING("默认音频设备不支持请求的格式，使用最接近的格式");
			format = info.nearestFormat(format);
			LOG_DEBUG("已调整: 采样率=" << format.sampleRate() << ", 声道数=" << format.channelCount()
										<< ", 样本大小=" << format.sampleSize());
		}

		// 创建音频输出
		audioOutput = new QAudioOutput(format);
		if (!audioOutput)
		{
			LOG_ERROR("无法创建QAudioOutput");
			return false;
		}

		// 设置缓冲区大小为0.5秒
		int bufferSize = (format.sampleRate() * format.channelCount() * format.sampleSize() / 8) / 2;
		audioOutput->setBufferSize(bufferSize);

		// 创建缓冲区和设备
		audioBuffer.clear();
		audioBufferDevice = new QBuffer(&audioBuffer);
		audioBufferDevice->open(QIODevice::ReadWrite);

		// 打开音频设备
		audioDevice = audioOutput->start();
		if (!audioDevice)
		{
			LOG_ERROR("无法启动音频输出");
			delete audioOutput;
			audioOutput = nullptr;
			delete audioBufferDevice;
			audioBufferDevice = nullptr;
			return false;
		}

		// 创建重采样上下文
		if (swrContext)
		{
			swr_free(&swrContext);
		}

		// 确定输入和输出格式
		AVSampleFormat inFormat = (AVSampleFormat)sampleFormat;
		AVSampleFormat outFormat = AV_SAMPLE_FMT_S16; // 16位有符号整数

		// 创建音频重采样上下文
		swrContext = swr_alloc_set_opts(
			nullptr,
			av_get_default_channel_layout(channels), // 输出声道布局
			outFormat,								 // 输出格式
			sampleRate,								 // 输出采样率
			av_get_default_channel_layout(channels), // 输入声道布局
			inFormat,								 // 输入格式
			sampleRate,								 // 输入采样率
			0,										 // 日志偏移
			nullptr									 // 日志上下文
		);

		if (!swrContext)
		{
			LOG_ERROR("无法创建音频重采样上下文");
			return false;
		}

		// 初始化重采样上下文
		if (swr_init(swrContext) < 0)
		{
			LOG_ERROR("无法初始化音频重采样上下文");
			swr_free(&swrContext);
			swrContext = nullptr;
			return false;
		}

		// 保存参数
		audioOutSampleRate = sampleRate;
		audioOutChannels = channels;
		audioInitialized = true;

		LOG_DEBUG("音频输出初始化成功");
		return true;
	}
	catch (const std::exception &e)
	{
		LOG_ERROR("音频初始化异常: " << e.what());
		return false;
	}
	catch (...)
	{
		LOG_ERROR("音频初始化未知异常");
		return false;
	}
}

// 清理音频输出资源
void MyThread::cleanupAudioOutput()
{
	LOG_DEBUG("清理音频输出资源");

	if (audioOutput)
	{
		audioOutput->stop();
		delete audioOutput;
		audioOutput = nullptr;
	}

	if (audioBufferDevice)
	{
		audioBufferDevice->close();
		delete audioBufferDevice;
		audioBufferDevice = nullptr;
	}

	audioBuffer.clear();
	audioDevice = nullptr;

	if (swrContext)
	{
		swr_free(&swrContext);
		swrContext = nullptr;
	}

	audioInitialized = false;
}

// 处理解码后的音频帧
void MyThread::processAudioFrame(AVFrame *frame)
{
	if (!frame || isMuted || !audioInitialized || !audioDevice)
	{
		return;
	}

	try
	{
		// 计算输出样本数量
		int outSamples = av_rescale_rnd(
			swr_get_delay(swrContext, frame->sample_rate) + frame->nb_samples,
			audioOutSampleRate,
			frame->sample_rate,
			AV_ROUND_UP);

		// 分配输出缓冲区
		uint8_t *outBuffer;
		int outLinesize;
		if (av_samples_alloc(&outBuffer, &outLinesize, audioOutChannels, outSamples, AV_SAMPLE_FMT_S16, 0) < 0)
		{
			LOG_ERROR("无法分配音频输出缓冲区");
			return;
		}

		// 执行重采样
		int samplesOut = swr_convert(
			swrContext,
			&outBuffer, outSamples,
			(const uint8_t **)frame->data, frame->nb_samples);

		if (samplesOut > 0)
		{
			// 计算数据大小（每个样本2字节，16位PCM）
			int dataSize = samplesOut * audioOutChannels * 2;

			// 清空缓冲区
			audioBufferDevice->seek(0);
			audioBuffer.clear();

			// 将数据写入缓冲区
			audioBufferDevice->write((const char *)outBuffer, dataSize);
			audioBufferDevice->seek(0);

			// 将数据写入音频设备
			if (audioDevice->write(audioBuffer) < 0)
			{
				LOG_ERROR("写入音频设备失败");
			}
		}

		// 释放输出缓冲区
		av_freep(&outBuffer);
	}
	catch (const std::exception &e)
	{
		LOG_ERROR("处理音频帧异常: " << e.what());
	}
	catch (...)
	{
		LOG_ERROR("处理音频帧未知异常");
	}
}

// 新增：使用外部ffmpeg.exe进行录像的方法
bool MyThread::startRecordingWithFFmpegExe(const QString &outputFile)
{
	if (!isRunning)
	{
		LOG_ERROR("线程未运行，无法开始录像");
		return false;
	}

	// 检查ffmpeg.exe是否存在
	QString ffmpegPath = QCoreApplication::applicationDirPath() + "/ffmpeg.exe";
	if (!QFile::exists(ffmpegPath))
	{
		LOG_ERROR("未找到ffmpeg.exe: " << ffmpegPath);
		return false;
	}

	// 停止之前的录像进程（如果有）
	stopRecordingWithFFmpegExe();

	// 创建录像进程
	recordProcess = new QProcess(this);

	// 连接进程结束信号，以便清理资源
	connect(recordProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
			[this](int exitCode, QProcess::ExitStatus exitStatus)
			{
				LOG_DEBUG("录像进程结束，退出码: " << exitCode);
				recordProcess->deleteLater();
				recordProcess = nullptr;
			});

	// 连接错误信号
	connect(recordProcess, &QProcess::errorOccurred,
			[this](QProcess::ProcessError error)
			{
				LOG_ERROR("录像进程错误: " << error);
			});

	// 设置命令行参数
	QStringList arguments;

	// 输入是RTSP流
	arguments << "-i" << rtspUrl;

	// 添加编码和输出选项
	arguments << "-c:v" << "copy" // 复制视频流，不重新编码
			  << "-c:a" << "copy" // 复制音频流，不重新编码
			  << "-y"			  // 覆盖已有文件
			  << outputFile;	  // 输出文件路径

	LOG_DEBUG("启动录像进程: " << ffmpegPath << arguments.join(" "));

	// 启动进程
	recordProcess->start(ffmpegPath, arguments);

	// 等待进程启动
	if (!recordProcess->waitForStarted(3000))
	{
		LOG_ERROR("录像进程启动失败");
		recordProcess->deleteLater();
		recordProcess = nullptr;
		return false;
	}

	LOG_INFO("录像已开始，输出到: " << outputFile);
	return true;
}

// 新增：停止外部ffmpeg.exe录像
void MyThread::stopRecordingWithFFmpegExe()
{
	if (recordProcess)
	{
// 发送退出信号
#ifdef Q_OS_WIN
		// Windows下使用taskkill强制结束进程
		QProcess::execute("taskkill", QStringList() << "/F" << "/PID" << QString::number(recordProcess->processId()));
#else
		// 其他系统使用terminate
		recordProcess->terminate();
		if (!recordProcess->waitForFinished(3000))
		{
			recordProcess->kill();
		}
#endif

		recordProcess->deleteLater();
		recordProcess = nullptr;
		LOG_INFO("录像已停止");
	}
}
