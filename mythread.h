#ifndef MYTHREAD_H
#define MYTHREAD_H

#include <QThread>
#include <QImage>
#include <QMutex>
#include <QTcpSocket>
#include <QProcess>
#include <QWaitCondition>
#include <QAudioOutput>
#include <QBuffer>
#include <atomic>

// FFmpeg头文件
extern "C"
{
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
#include <libavutil/imgutils.h>
#include <libswresample/swresample.h>
}

class MyThread : public QThread
{
	Q_OBJECT
public:
	explicit MyThread(QObject *parent = NULL);
	explicit MyThread(QString ip, int port, QObject *parent = NULL);
	explicit MyThread(QString rtspUrl, QObject *parent = NULL);
	~MyThread();
	void setRunning(bool running);
	QImage getLastFrame() const;
	bool isThreadRunning() const { return isRunning; }
	void setMuted(bool muted);

	// 新增: 安全停止线程，等待线程结束
	bool safeStop(int timeoutMs = 3000);

	// 测试HTTP图像是否有效
	static bool testHttpImage(const QString &ip, const QString &username = "",
							  const QString &password = "", int timeoutMs = 3000);

	// 从IP构建HTTP图像URL
	static QString buildHttpImageUrl(const QString &ip, const QString &username = "",
									 const QString &password = "");

	// 新增：使用外部ffmpeg.exe进行录像
	bool startRecordingWithFFmpegExe(const QString &outputFile);
	void stopRecordingWithFFmpegExe();

signals:
	void transmitData(QImage image);
	void disconnectSlot();

protected:
	void run() Q_DECL_OVERRIDE;
	void handleTcpConnection();
	void handleRtspWithFFmpeg();

private:
	QString ip;
	int port;
	QString rtspUrl;
	std::atomic<bool> isRunning;
	mutable QMutex mutex;
	QImage lastFrame;
	std::atomic<bool> isMuted;

	// 新增: 用于线程同步
	std::atomic<bool> m_threadStopped;

	// 新增：录像相关变量
	QProcess *recordProcess;

	// 辅助方法，用于清理媒体资源
	void cleanupMediaResources();

	// FFmpeg相关成员
	AVFormatContext *formatContext;
	AVCodecContext *codecContext;
	AVFrame *frame;
	AVPacket *packet;
	SwsContext *swsContext;
	int videoStreamIndex;

	// 音频输出相关成员
	QAudioOutput *audioOutput;
	QIODevice *audioDevice;
	QByteArray audioBuffer;
	QBuffer *audioBufferDevice;
	SwrContext *swrContext;
	int audioOutSampleRate;
	int audioOutChannels;
	bool audioInitialized;

	// FFmpeg辅助方法
	bool initFFmpeg();
	void cleanupFFmpeg();
	bool initAudioOutput(int sampleRate, int channels, int sampleFormat);
	void cleanupAudioOutput();
	void processAudioFrame(AVFrame *frame);

private slots:
	void handleSocketError();
};

#endif // MYTHREAD_H
